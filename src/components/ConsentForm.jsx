import { useState, useRef } from 'react'
import SignatureCanvas from 'react-signature-canvas'
import jsPDF from 'jspdf'
import html2canvas from 'html2canvas'
import { FormHeader, SectionRenderer } from './FormRenderer'
import './ConsentForm.css'

const ConsentForm = ({ consentData }) => {
  const [participantName, setParticipantName] = useState('')
  const [date, setDate] = useState(new Date().toISOString().split('T')[0])
  const [isSigned, setIsSigned] = useState(false)
  const sigCanvas = useRef({})
  const formRef = useRef()

  const clearSignature = () => {
    sigCanvas.current.clear()
    setIsSigned(false)
  }

  const handleSignatureEnd = () => {
    setIsSigned(!sigCanvas.current.isEmpty())
  }



  const createSignatureImage = () => {
    // Create a new canvas with white background to ensure visibility
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')

    // Set canvas size to match signature canvas
    canvas.width = 500
    canvas.height = 200

    // Fill with white background
    ctx.fillStyle = '#ffffff'
    ctx.fillRect(0, 0, canvas.width, canvas.height)

    // Get the original signature canvas
    const originalCanvas = sigCanvas.current.getCanvas()

    // Verify the original canvas has actual signature data
    const originalImageData = originalCanvas.getContext('2d').getImageData(0, 0, originalCanvas.width, originalCanvas.height)
    const pixels = originalImageData.data
    let hasNonWhitePixels = false

    // Check for non-white pixels (signature content)
    for (let i = 0; i < pixels.length; i += 4) {
      const r = pixels[i]
      const g = pixels[i + 1]
      const b = pixels[i + 2]
      const a = pixels[i + 3]

      // If we find a pixel that's not white or transparent, we have signature content
      if (a > 0 && (r < 255 || g < 255 || b < 255)) {
        hasNonWhitePixels = true
        break
      }
    }

    if (!hasNonWhitePixels) {
      console.error('❌ No signature content detected in canvas')
      throw new Error('Signature canvas appears to be empty')
    }

    // Draw the signature on top of the white background
    ctx.drawImage(originalCanvas, 0, 0)

    // Get the final image data
    const signatureData = canvas.toDataURL('image/png')

    console.log('🖊️ Signature data length:', signatureData.length)
    console.log('🖊️ Signature data preview:', signatureData.substring(0, 100))

    // Additional validation: check if signature data is substantial
    if (signatureData.length < 2000) {
      console.error('❌ Signature data appears too small')
      throw new Error('Generated signature image is too small to be valid')
    }

    console.log('✅ Signature data validation passed!')
    return Promise.resolve(signatureData)
  }

  const generatePDF = async () => {
    if (!participantName.trim()) {
      alert('Please enter participant name')
      return
    }

    // Only validate signature if it's required
    if (formData.signature.required) {
      if (!isSigned) {
        alert('Please provide your signature')
        return
      }

      // Additional signature validation - check if canvas is truly empty
      if (sigCanvas.current.isEmpty()) {
        alert('Please provide your signature before generating PDF')
        setIsSigned(false)
        return
      }
    }

    try {
      // Create clean signature image with validation (only if signature is required)
      let signatureImageData = null
      if (formData.signature.required) {
        try {
          signatureImageData = await createSignatureImage()
        } catch (signatureError) {
          console.error('❌ Signature validation failed:', signatureError.message)
          alert(`Signature validation failed: ${signatureError.message}. Please ensure you have provided a clear signature.`)
          return
        }
      }

      console.log('📄 Generating PDF using reliable image-based approach...')

      // Hide form controls during capture
      const controls = document.querySelectorAll('.form-controls')
      controls.forEach(control => control.style.display = 'none')

      // Capture the form as image with high quality
      const canvas = await html2canvas(formRef.current, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        logging: false
      })

      // Show controls again
      controls.forEach(control => control.style.display = 'block')

      // Create a composite canvas with form + signature
      const compositeCanvas = document.createElement('canvas')
      const ctx = compositeCanvas.getContext('2d')

      compositeCanvas.width = canvas.width
      compositeCanvas.height = canvas.height

      // Draw the form
      ctx.drawImage(canvas, 0, 0)

      // Find signature position on the captured canvas
      const signatureContainer = document.querySelector('.signature-canvas-container')
      const formRect = formRef.current.getBoundingClientRect()
      const sigRect = signatureContainer.getBoundingClientRect()

      // Calculate relative position and scale for the high-res canvas
      const scale = 2
      const relativeTop = (sigRect.top - formRect.top) * scale
      const relativeLeft = (sigRect.left - formRect.left) * scale
      const signatureWidth = 500 * scale
      const signatureHeight = 200 * scale

      console.log('📄 PDF signature positioning:', {
        relativeTop,
        relativeLeft,
        signatureWidth,
        signatureHeight
      })

      // Function to generate final PDF
      const generateFinalPDF = () => {
        // Convert composite to PDF
        const finalImageData = compositeCanvas.toDataURL('image/png')

        const pdf = new jsPDF('p', 'mm', 'a4')
        const pdfWidth = pdf.internal.pageSize.getWidth()
        const pdfHeight = pdf.internal.pageSize.getHeight()

        // Calculate image dimensions to fit PDF page
        const imgWidth = compositeCanvas.width
        const imgHeight = compositeCanvas.height
        const ratio = Math.min(pdfWidth / (imgWidth * 0.264583), pdfHeight / (imgHeight * 0.264583))

        const finalWidth = imgWidth * 0.264583 * ratio
        const finalHeight = imgHeight * 0.264583 * ratio

        // Center the image on the PDF page
        const x = (pdfWidth - finalWidth) / 2
        const y = (pdfHeight - finalHeight) / 2

        // Add the composite image to PDF
        pdf.addImage(finalImageData, 'PNG', x, y, finalWidth, finalHeight)

        // Save the PDF
        const fileName = `${consentData?.metadata?.fileNamePrefix || 'Consent'}_${participantName.replace(/\s+/g, '_')}_${date}.pdf`
        pdf.save(fileName)

        console.log('✅ PDF generated successfully!')
        alert(`PDF generated successfully${formData.signature.required ? ' with signature' : ''}!`)
      }

      // If signature is required and available, composite it
      if (formData.signature.required && signatureImageData) {
        const signatureImg = new Image()
        signatureImg.onload = () => {
          // Draw signature on the composite canvas
          ctx.drawImage(signatureImg, relativeLeft, relativeTop, signatureWidth, signatureHeight)
          generateFinalPDF()
        }

        signatureImg.onerror = () => {
          console.error('❌ Failed to load signature for PDF composite')
          alert('Error adding signature to PDF. Please try again.')
        }

        signatureImg.src = signatureImageData
      } else {
        // No signature required or available, generate PDF directly
        generateFinalPDF()
      }

    } catch (error) {
      console.error('Error generating PDF:', error)
      alert('Error generating PDF. Please try again.')
    }
  }



  // Provide default consent data if none provided
  const defaultConsentData = {
    metadata: { fileNamePrefix: 'Consent' },
    header: { title: 'Consent Form', subtitle: '' },
    sections: [],
    signature: {
      required: true,
      label: 'Participant Signature:',
      participantNameLabel: 'Participant Name:',
      dateLabel: 'Date:',
      signatureLineText: 'Signature'
    }
  }

  const formData = consentData || defaultConsentData

  return (
    <div className="consent-container">
      <div ref={formRef} className="consent-form">
        {/* Dynamic header */}
        <FormHeader header={formData.header} />

        {/* Dynamic sections */}
        {formData.sections.map((section) => (
          <SectionRenderer key={section.id} section={section} />
        ))}

        {/* Participant information section - always shown */}
        <section className="signature-section">
          <div className="participant-info">
            <div className="input-group">
              <label htmlFor="participantName">{formData.signature.participantNameLabel}</label>
              <input
                type="text"
                id="participantName"
                value={participantName}
                onChange={(e) => setParticipantName(e.target.value)}
                placeholder="Enter full name"
                required
              />
            </div>
            <div className="input-group">
              <label htmlFor="date">{formData.signature.dateLabel}</label>
              <input
                type="date"
                id="date"
                value={date}
                onChange={(e) => setDate(e.target.value)}
                required
              />
            </div>
          </div>

          {/* Signature area - only shown if required */}
          {formData.signature.required && (
            <div className="signature-area">
              <label>{formData.signature.label}</label>
              <div className="signature-canvas-container">
                <SignatureCanvas
                  ref={sigCanvas}
                  penColor="#000000"
                  backgroundColor="#ffffff"
                  canvasProps={{
                    width: 500,
                    height: 200,
                    className: 'signature-canvas',
                    style: { backgroundColor: '#ffffff' }
                  }}
                  onEnd={handleSignatureEnd}
                  clearOnResize={false}
                />
              </div>
              <div className="signature-line">
                <span>{formData.signature.signatureLineText}</span>
              </div>
            </div>
          )}
        </section>
      </div>

      <div className="form-controls">
        {formData.signature.required && (
          <button onClick={clearSignature} className="btn btn-secondary">
            Clear Signature
          </button>
        )}
        <button onClick={generatePDF} className="btn btn-primary">
          Save
        </button>
      </div>
    </div>
  )
}

export default ConsentForm
