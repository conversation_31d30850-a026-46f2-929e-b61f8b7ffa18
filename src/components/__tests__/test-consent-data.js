// Test data for consent form tests
export const testConsentData = {
  metadata: {
    id: "test-consent",
    version: "1.0.0",
    fileNamePrefix: "Test_Consent",
    description: "Test consent form"
  },
  header: {
    title: "Test Consent Form",
    subtitle: "This is a test consent form for unit testing.",
    organization: "Test Organization"
  },
  sections: [
    {
      id: "test-section-1",
      title: "Test Section",
      type: "text",
      content: "This is a test section with some text content."
    },
    {
      id: "test-section-2",
      title: "Test List Section",
      type: "list",
      content: [
        "First list item",
        "Second list item",
        {
          text: "Third item with sub-items:",
          subItems: [
            "Sub-item 1",
            "Sub-item 2"
          ]
        }
      ]
    },
    {
      id: "test-contact",
      title: "Contact Information",
      type: "contact",
      content: {
        description: "For questions, please contact:",
        phone: "************",
        email: "<EMAIL>"
      }
    }
  ],
  signature: {
    required: true,
    label: "Participant Signature:",
    participantNameLabel: "Participant Name:",
    dateLabel: "Date:",
    signatureLineText: "Signature"
  },
  styling: {
    primaryColor: "#2c5aa0",
    fontFamily: "Arial, sans-serif"
  }
}

// Test data for a form without signature requirement
export const noSignatureConsentData = {
  metadata: {
    id: "no-sig-consent",
    version: "1.0.0",
    fileNamePrefix: "NoSig_Consent",
    description: "Test consent form without signature"
  },
  header: {
    title: "No Signature Consent Form",
    subtitle: "This form does not require a signature."
  },
  sections: [
    {
      id: "info-section",
      title: "Information",
      type: "text",
      content: "This is an informational consent form that does not require a signature."
    }
  ],
  signature: {
    required: false,
    label: "Participant Signature:",
    participantNameLabel: "Participant Name:",
    dateLabel: "Date:",
    signatureLineText: "Signature"
  }
}
