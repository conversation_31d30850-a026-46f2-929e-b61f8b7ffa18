import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest'
import React from 'react'
import ConsentForm from '../ConsentForm'
import { testConsentData, noSignatureConsentData } from './test-consent-data'

// Mock jsPDF
const mockSave = vi.fn()
const mockAddImage = vi.fn()
const mockAddPage = vi.fn()
vi.mock('jspdf', () => ({
  default: vi.fn().mockImplementation(() => ({
    internal: {
      pageSize: {
        getWidth: () => 210,
        getHeight: () => 297
      }
    },
    addImage: mockAddImage,
    addPage: mockAddPage,
    save: mockSave
  }))
}))

// Mock html2canvas
vi.mock('html2canvas', () => ({
  default: vi.fn(() => Promise.resolve({
    toDataURL: () => 'data:image/png;base64,mockFormImage',
    width: 800,
    height: 1000
  }))
}))

// Mock react-signature-canvas
const mockClear = vi.fn()
const mockIsEmpty = vi.fn()
const mockGetCanvas = vi.fn()
const mockToDataURL = vi.fn()

vi.mock('react-signature-canvas', () => ({
  default: React.forwardRef((props, ref) => {
    // Simulate the ref methods
    if (ref) {
      ref.current = {
        clear: mockClear,
        isEmpty: mockIsEmpty,
        getCanvas: mockGetCanvas,
        toDataURL: mockToDataURL
      }
    }

    return (
      <canvas
        data-testid="signature-canvas"
        width={props.canvasProps?.width || 500}
        height={props.canvasProps?.height || 200}
        onClick={() => props.onEnd && props.onEnd()}
        style={props.canvasProps?.style}
      />
    )
  })
}))

describe('ConsentForm Signature Validation', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // Default mock implementations
    mockIsEmpty.mockReturnValue(false)
    // Create a longer base64 string to simulate a valid signature (needs to be > 2000 chars)
    const longBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='.repeat(50)
    mockToDataURL.mockReturnValue(`data:image/png;base64,${longBase64}`)
    
    // Mock canvas with signature content
    const mockCanvas = {
      width: 500,
      height: 200,
      getContext: vi.fn(() => ({
        getImageData: vi.fn(() => ({
          data: new Uint8ClampedArray(4 * 500 * 200).fill(0).map((_, i) => {
            // Create some non-white pixels to simulate signature
            if (i % 4 === 3) return 255 // Alpha channel
            return i % 100 < 50 ? 0 : 255 // Some black pixels mixed with white
          })
        }))
      }))
    }
    mockGetCanvas.mockReturnValue(mockCanvas)

    // Mock getBoundingClientRect for form elements
    Element.prototype.getBoundingClientRect = vi.fn(() => ({
      top: 100,
      left: 50,
      width: 800,
      height: 1000
    }))

    // Mock offsetWidth and offsetHeight
    Object.defineProperty(HTMLElement.prototype, 'offsetWidth', {
      configurable: true,
      value: 800
    })
    Object.defineProperty(HTMLElement.prototype, 'offsetHeight', {
      configurable: true,
      value: 1000
    })
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  it('should prevent PDF generation when participant name is empty', async () => {
    render(<ConsentForm consentData={testConsentData} />)

    const saveButton = screen.getByText('Save')
    fireEvent.click(saveButton)

    expect(global.alert).toHaveBeenCalledWith('Please enter participant name')
    expect(mockSave).not.toHaveBeenCalled()
  })

  it('should prevent PDF generation when signature is empty', async () => {
    mockIsEmpty.mockReturnValue(true)

    render(<ConsentForm consentData={testConsentData} />)

    const nameInput = screen.getByLabelText('Participant Name:')
    await userEvent.type(nameInput, 'John Doe')

    const saveButton = screen.getByText('Save')
    fireEvent.click(saveButton)

    expect(global.alert).toHaveBeenCalledWith('Please provide your signature')
    expect(mockSave).not.toHaveBeenCalled()
  })

  it('should validate signature content before PDF generation', async () => {
    // Mock empty signature canvas (all white pixels)
    const emptyCanvas = {
      width: 500,
      height: 200,
      getContext: vi.fn(() => ({
        getImageData: vi.fn(() => ({
          data: new Uint8ClampedArray(4 * 500 * 200).fill(255) // All white pixels
        }))
      }))
    }
    mockGetCanvas.mockReturnValue(emptyCanvas)
    mockIsEmpty.mockReturnValue(false) // Canvas thinks it has content but pixels are all white
    
    render(<ConsentForm consentData={testConsentData} />)

    const nameInput = screen.getByLabelText('Participant Name:')
    await userEvent.type(nameInput, 'John Doe')
    
    // Simulate signing (this will trigger handleSignatureEnd)
    const signatureCanvas = screen.getByTestId('signature-canvas')
    fireEvent.click(signatureCanvas)
    
    const saveButton = screen.getByText('Save')
    fireEvent.click(saveButton)
    
    await waitFor(() => {
      expect(global.alert).toHaveBeenCalledWith(
        expect.stringContaining('Signature validation failed: Signature canvas appears to be empty')
      )
    })
    
    expect(mockSave).not.toHaveBeenCalled()
  })

  it('should attempt PDF generation with valid signature and name', async () => {
    // Override the mock for this test to ensure it returns a large enough signature
    const longBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='.repeat(100)
    global.HTMLCanvasElement.prototype.toDataURL = vi.fn(() => `data:image/png;base64,${longBase64}`)

    render(<ConsentForm consentData={testConsentData} />)

    const nameInput = screen.getByLabelText('Participant Name:')
    await userEvent.type(nameInput, 'John Doe')

    // Simulate signing
    const signatureCanvas = screen.getByTestId('signature-canvas')
    fireEvent.click(signatureCanvas)

    const saveButton = screen.getByText('Save')

    // The test should not show any validation errors when clicking save
    // Since the PDF generation involves complex DOM operations that are hard to mock,
    // we'll just verify that no validation alerts are shown
    fireEvent.click(saveButton)

    // Wait a bit to ensure any synchronous validation has run
    await new Promise(resolve => setTimeout(resolve, 100))

    // Verify no validation error alerts were shown
    expect(global.alert).not.toHaveBeenCalledWith('Please enter participant name')
    expect(global.alert).not.toHaveBeenCalledWith('Please provide your signature')
    expect(global.alert).not.toHaveBeenCalledWith(
      expect.stringContaining('Signature validation failed')
    )
  })

  it('should validate signature image data size', async () => {
    // Mock signature that's too small by overriding the canvas toDataURL
    global.HTMLCanvasElement.prototype.toDataURL = vi.fn(() => 'data:image/png;base64,tiny')

    render(<ConsentForm consentData={testConsentData} />)

    const nameInput = screen.getByLabelText('Participant Name:')
    await userEvent.type(nameInput, 'John Doe')

    const signatureCanvas = screen.getByTestId('signature-canvas')
    fireEvent.click(signatureCanvas)

    const saveButton = screen.getByText('Save')
    fireEvent.click(saveButton)

    await waitFor(() => {
      expect(global.alert).toHaveBeenCalledWith(
        expect.stringContaining('Generated signature image is too small to be valid')
      )
    })

    expect(mockSave).not.toHaveBeenCalled()
  })

  it('should clear signature and update state correctly', () => {
    render(<ConsentForm consentData={testConsentData} />)

    // Simulate signing first
    const signatureCanvas = screen.getByTestId('signature-canvas')
    fireEvent.click(signatureCanvas)

    // Clear signature
    const clearButton = screen.getByText('Clear Signature')
    fireEvent.click(clearButton)

    expect(mockClear).toHaveBeenCalled()
  })

  it('should update signature state when signature ends', () => {
    render(<ConsentForm consentData={testConsentData} />)

    const signatureCanvas = screen.getByTestId('signature-canvas')

    // Test with non-empty signature
    mockIsEmpty.mockReturnValue(false)
    fireEvent.click(signatureCanvas)

    // Test with empty signature
    mockIsEmpty.mockReturnValue(true)
    fireEvent.click(signatureCanvas)
  })

  it('should render form without signature when not required', () => {
    render(<ConsentForm consentData={noSignatureConsentData} />)

    // Should not render signature canvas or clear button
    expect(screen.queryByTestId('signature-canvas')).not.toBeInTheDocument()
    expect(screen.queryByText('Clear Signature')).not.toBeInTheDocument()

    // Should still have Save button
    expect(screen.getByText('Save')).toBeInTheDocument()
  })

  it('should generate PDF without signature validation when signature not required', async () => {
    render(<ConsentForm consentData={noSignatureConsentData} />)

    const nameInput = screen.getByLabelText('Participant Name:')
    await userEvent.type(nameInput, 'John Doe')

    const saveButton = screen.getByText('Save')
    fireEvent.click(saveButton)

    // Should not show signature validation errors
    expect(global.alert).not.toHaveBeenCalledWith('Please provide your signature')
  })

  it('should render dynamic content from JSON data', () => {
    render(<ConsentForm consentData={testConsentData} />)

    // Check header content
    expect(screen.getByText('Test Consent Form')).toBeInTheDocument()
    expect(screen.getByText(/This is a test consent form for unit testing/)).toBeInTheDocument()

    // Check section content
    expect(screen.getByText('Test Section')).toBeInTheDocument()
    expect(screen.getByText('This is a test section with some text content.')).toBeInTheDocument()

    // Check list content
    expect(screen.getByText('Test List Section')).toBeInTheDocument()
    expect(screen.getByText('First list item')).toBeInTheDocument()
    expect(screen.getByText('Second list item')).toBeInTheDocument()

    // Check contact content
    expect(screen.getByText('Contact Information')).toBeInTheDocument()
    expect(screen.getByText('555-123-4567')).toBeInTheDocument()
  })
})
